import {
  _CONTACT_CHANNEL_OPTIONS,
  _FOLLOW_UP_STATUS_OPTIONS,
  _OPPORTUNITY_OPTIONS,
  _SERVICE_OPTIONS,
} from "@components/badge";
import { Button, Input, Select, Textarea } from "@components/common";
import { useState } from "react";
import { useTranslation } from "react-i18next";

type FieldType = "input" | "select" | "date" | "textarea";

interface BaseField {
  id: string;
  label: string;
  type: FieldType;
  required?: boolean;
  colSpan?: 1 | 2 | 3;
  gridCols?: 2 | 3;
  placeholder?: string;
  disabled?: boolean;
}

interface InputField extends BaseField {
  type: "input" | "date";
  inputType?: string;
  defaultValue?: string;
  variant?: "default" | "transparent";
}

interface SelectField extends BaseField {
  type: "select";
  options: Array<{ label: React.ReactNode; value: string }>;
  value: string | null;
  onChange: (value: string) => void;
}

interface TextareaField extends BaseField {
  type: "textarea";
  className?: string;
}

type FormField = InputField | SelectField | TextareaField;

export const AddLeadForm = () => {
  const { t } = useTranslation();

  const today = new Date().toISOString().split("T")[0];

  const [followUpStatus, setFollowUpStatus] = useState<string | null>(null);
  const [opportunity, setOpportunity] = useState<string | null>(null);
  const [contactChannel, setContactChannel] = useState<string | null>(null);
  const [servicesOfInterest, setServicesOfInterest] = useState<string | null>(null);

  const formFields: FormField[] = [
    {
      colSpan: 2,
      gridCols: 3,
      id: "name",
      label: t("addLead.name"),
      placeholder: t("addLead.name"),
      required: true,
      type: "input",
      variant: "transparent",
    },
    {
      colSpan: 1,
      gridCols: 2,
      id: "opportunity",
      label: t("addLead.opportunity"),
      onChange: setOpportunity,
      options: _OPPORTUNITY_OPTIONS,
      placeholder: t("addLead.opportunity"),
      type: "select",
      value: opportunity,
    },
    {
      colSpan: 2,
      gridCols: 3,
      id: "followUpStatus",
      label: t("addLead.followUpStatus"),
      onChange: setFollowUpStatus,
      options: _FOLLOW_UP_STATUS_OPTIONS,
      placeholder: t("addLead.followUpStatus"),
      type: "select",
      value: followUpStatus,
    },
    {
      colSpan: 1,
      gridCols: 2,
      id: "contactChannel",
      label: t("addLead.contactChannel"),
      onChange: setContactChannel,
      options: _CONTACT_CHANNEL_OPTIONS,
      placeholder: t("addLead.contactChannel"),
      required: true,
      type: "select",
      value: contactChannel,
    },
    {
      colSpan: 2,
      gridCols: 3,
      id: "servicesOfInterest",
      label: t("addLead.servicesOfInterest"),
      onChange: setServicesOfInterest,
      options: _SERVICE_OPTIONS,
      placeholder: t("addLead.servicesOfInterest"),
      type: "select",
      value: servicesOfInterest,
    },
    {
      colSpan: 1,
      defaultValue: today,
      gridCols: 2,
      id: "startDate",
      label: t("addLead.startDate"),
      type: "date",
    },
    {
      colSpan: 2,
      gridCols: 3,
      id: "contactInfo",
      label: t("addLead.contactInfo"),
      placeholder: t("addLead.contactInfo"),
      type: "input",
      variant: "transparent",
    },
    {
      colSpan: 1,
      defaultValue: "-",
      disabled: true,
      gridCols: 2,
      id: "followUpDate",
      label: t("addLead.followUpDate"),
      type: "input",
    },
  ];

  const textareaField: TextareaField = {
    className: "min-h-24 w-full flex-1 resize-none",
    id: "note",
    label: t("addLead.note"),
    placeholder: t("addLead.addAdditionalDescription"),
    type: "textarea",
  };

  // Render field component based on type
  const renderField = (field: FormField) => {
    const BASE_CLASSES = field.colSpan ? `col-span-${field.colSpan}` : "";
    const GRID_CLASSES = field.gridCols ? `grid-cols-${field.gridCols}` : "grid-cols-2";
    const CONTAINER_CLASSES = `${BASE_CLASSES} grid ${GRID_CLASSES}`;

    const labelContent = (
      <label htmlFor={field.id} className={`text-h6 ${field.required ? "flex gap-1" : ""}`}>
        <span className="text-h6">{field.label}</span>
        {field.required && <span className="text-error text-h6">*</span>}
      </label>
    );

    switch (field.type) {
      case "input": {
        const inputField = field;
        return (
          <div key={field.id} className={`group ${CONTAINER_CLASSES}`}>
            {labelContent}
            <Input
              id={field.id}
              type="text"
              placeholder={inputField.placeholder}
              defaultValue={inputField.defaultValue}
              disabled={field.disabled}
              className={`${field.gridCols === 3 ? "col-span-2" : ""} flex-1 group-hover:bg-base-200`}
              variant={inputField.variant || "default"}
            />
          </div>
        );
      }

      case "date": {
        const dateField = field;
        return (
          <div key={field.id} className={CONTAINER_CLASSES}>
            {labelContent}
            <div className="relative">
              <Input
                id={field.id}
                type="date"
                defaultValue={dateField.defaultValue}
                disabled={field.disabled}
                className="w-full"
              />
            </div>
          </div>
        );
      }

      case "select": {
        const selectField = field;
        return (
          <div key={field.id} className={CONTAINER_CLASSES}>
            {labelContent}
            {field.gridCols === 3 ? (
              <div className="col-span-2">
                <Select
                  id={field.id}
                  options={selectField.options}
                  size="sm"
                  variant="popup"
                  value={selectField.value ?? "-"}
                  onChange={selectField.onChange}
                  className="flex-1"
                  placeholder={selectField.placeholder}
                />
              </div>
            ) : (
              <Select
                id={field.id}
                options={selectField.options}
                size="sm"
                variant="popup"
                value={selectField.value ?? "-"}
                onChange={selectField.onChange}
                placeholder={selectField.placeholder}
              />
            )}
          </div>
        );
      }

      case "textarea": {
        const textareaFieldTyped = field;
        return (
          <div key={field.id} className="flex min-h-0 flex-1 flex-col gap-2">
            <label htmlFor={field.id} className="text-h6">
              {field.label}
            </label>
            <Textarea
              id={field.id}
              placeholder={textareaFieldTyped.placeholder}
              className={textareaFieldTyped.className}
            />
          </div>
        );
      }

      default:
        return null;
    }
  };

  return (
    <form className="flex h-full flex-col gap-6 overflow-hidden p-1">
      <div className="space-y-4">
        <div className="grid grid-cols-3 gap-4">{formFields.map(renderField)}</div>
      </div>

      {/* บันทึก */}
      {renderField(textareaField)}

      {/* Buttons */}
      <div className="flex w-full justify-end gap-3 pt-4">
        <Button variant="outline" className="w-28">
          {t("common.draft")}
        </Button>
        <Button className="w-28">{t("common.save")}</Button>
      </div>
    </form>
  );
};
