# Generic Badge Component

## Overview

The `Badge` component has been redesigned as a generic, reusable component that can be configured for any badge type. This approach provides maximum flexibility while maintaining type safety and consistency.

## Key Features

- **Type Safety**: Full TypeScript support with generic types
- **Reusable**: One component for all badge types
- **Configurable**: Easy to create new badge variants
- **Consistent**: Unified styling and behavior
- **Flexible**: Custom styling support

## Basic Usage

### 1. Import the Generic Badge

```tsx
import { Badge, type BadgeConfig } from "@components/badge/badge";
```

### 2. Define Your Badge Types

```tsx
export type StatusType = "success" | "pending" | "failed";
```

### 3. Create Configuration Object

```tsx
const STATUS_BADGE_CONFIG: Record<StatusType, BadgeConfig> = {
  success: {
    label: "SUCCESS",
    icon: checkIcon,
    iconAlt: "Check",
    containerClasses: "border-success bg-success/10",
    textClasses: "text-success",
  },
  pending: {
    label: "PENDING",
    icon: clockIcon,
    iconAlt: "Clock",
    containerClasses: "border-warning bg-warning/10",
    textClasses: "text-warning",
  },
  failed: {
    label: "FAILED",
    icon: xIcon,
    iconAlt: "X",
    containerClasses: "border-error bg-error/10",
    textClasses: "text-error",
  },
};
```

### 4. Use the Badge Component

```tsx
<Badge
  type="success"
  config={STATUS_BADGE_CONFIG}
  className="shadow-lg"
/>
```

## Creating Specific Badge Components

For better developer experience, you can create specific badge components:

```tsx
interface StatusBadgeProps {
  type?: StatusType;
  className?: string;
}

export const StatusBadge = ({ type, className }: StatusBadgeProps) => {
  return (
    <Badge
      type={type}
      className={className}
      config={STATUS_BADGE_CONFIG}
    />
  );
};
```

## Examples

### Status Badge

```tsx
import { StatusBadge } from "@components/badge/examples/statusBadge";

<StatusBadge type="success" />
<StatusBadge type="pending" />
<StatusBadge type="failed" />
```

### Priority Badge

```tsx
import { PriorityBadge } from "@components/badge/examples/priorityBadge";

<PriorityBadge type="high" />
<PriorityBadge type="medium" />
<PriorityBadge type="low" />
```

### Custom Badge

```tsx
type CustomType = "alpha" | "beta" | "stable";

const CUSTOM_CONFIG: Record<CustomType, BadgeConfig> = {
  alpha: {
    label: "ALPHA",
    icon: alphaIcon,
    iconAlt: "Alpha",
    containerClasses: "border-purple-500 bg-purple-100",
    textClasses: "text-purple-700",
  },
  // ... other configurations
};

<Badge type="alpha" config={CUSTOM_CONFIG} />
```

## API Reference

### Badge Props

| Prop | Type | Description |
|------|------|-------------|
| `type` | `T extends string` | The badge type to render |
| `className` | `string` | Additional CSS classes |
| `config` | `Record<T, BadgeConfig>` | Configuration object for all badge types |

### BadgeConfig Interface

```tsx
interface BadgeConfig {
  label: string;           // Text to display
  icon: string;           // Icon image source
  iconAlt: string;        // Alt text for icon
  containerClasses: string; // CSS classes for container
  textClasses: string;    // CSS classes for text
}
```

## Migration from Specific Components

### Before (OpportunityBadge)

```tsx
import { OpportunityBadge } from "@components/badge/opportunityBadge";

<OpportunityBadge type="hot" />
```

### After (Generic Badge)

```tsx
import { Badge } from "@components/badge/badge";
import { OPPORTUNITY_CONFIG } from "@components/badge/configs/opportunity";

<Badge type="hot" config={OPPORTUNITY_CONFIG} />
```

Or create a wrapper:

```tsx
export const OpportunityBadge = ({ type, className }: OpportunityBadgeProps) => {
  return <Badge type={type} config={OPPORTUNITY_CONFIG} className={className} />;
};
```

## Benefits

1. **Single Source of Truth**: One component for all badge types
2. **Type Safety**: Generic types prevent runtime errors
3. **Consistency**: Unified styling and behavior
4. **Maintainability**: Easy to update and extend
5. **Performance**: No code duplication
6. **Flexibility**: Easy to create new badge types

## Best Practices

1. **Create Wrapper Components**: For better DX and easier imports
2. **Use Descriptive Types**: Make badge types self-documenting
3. **Consistent Naming**: Follow naming conventions for types and configs
4. **Centralize Configs**: Keep configuration objects in separate files
5. **Document Types**: Add JSDoc comments for complex badge types

## Testing

```tsx
import { render, screen } from "@testing-library/react";
import { Badge } from "@components/badge/badge";

const TEST_CONFIG = {
  test: {
    label: "TEST",
    icon: "test-icon.png",
    iconAlt: "Test",
    containerClasses: "test-container",
    textClasses: "test-text",
  },
};

test("renders badge correctly", () => {
  render(<Badge type="test" config={TEST_CONFIG} />);
  
  expect(screen.getByText("TEST")).toBeInTheDocument();
  expect(screen.getByAltText("Test")).toBeInTheDocument();
});
```
